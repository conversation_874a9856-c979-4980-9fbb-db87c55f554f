# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an iOS educational/e-learning platform app ("ULearning") with a hybrid architecture combining native Objective-C and web-based views via Apache Cordova.

## Build Commands

```bash
# Install dependencies
pod install

# Open workspace (IMPORTANT: use workspace, not project)
open yxy.xcworkspace

# Build and run via Xcode UI
# Select target device/simulator and press Cmd+R
```

## Architecture

**MVVM Pattern** with ReactiveObjC:
- **Models**: `yxy/Classes/Models/` - Data models using JSONModel
- **Views**: XIB files in `yxy/Classes/Views/` and view controllers
- **ViewModels**: Base class `BaseViewModel` in `yxy/Classes/Common/`
- **Hybrid Views**: `UBaseCordovaViewController` for H5/web content

**Key Base Classes**:
- `BaseViewController`: All native view controllers inherit from this
- `UBaseCordovaViewController`: For hybrid web views
- `BaseViewModel`: ViewModel base with ReactiveObjC bindings

## Project Structure

```
yxy/Classes/
├── Common/          # Base classes, utilities, categories
├── Models/          # Data models
├── Views/           # Custom views and XIBs
├── Modules/         # Feature modules (Login, Course, Live, etc.)
└── Vendor/          # Third-party code not in Pods

Plugins/             # Cordova plugins for H5 integration
CordovaLib/          # Apache Cordova framework
```

## Key Dependencies

- **UI**: Masonry (Auto Layout), MJRefresh, SVProgressHUD
- **Networking**: AFNetworking (via JSONModel)
- **Media**: ijkplayer, custom live streaming SDKs
- **Storage**: FMDB (SQLite), Keychain utilities
- **Push/Analytics**: JPush, UMeng
- **Social**: WeChat SDK, Tencent SDK

## Development Practices

1. **Language**: Primary Objective-C, minimal Swift
2. **Reactive Programming**: Use ReactiveObjC for data binding
3. **H5 Integration**: Cordova plugins in `Plugins/` directory
4. **Localization**: Multiple languages supported, strings in `.lproj` files
5. **Security**: App includes anti-debugging and certificate pinning

## Common Tasks

**Adding a new Cordova plugin**:
1. Create plugin class in `Plugins/` directory
2. Register in Cordova's config
3. Implement JavaScript interface

**Creating a new module**:
1. Follow existing module structure in `yxy/Classes/Modules/`
2. Create ViewController, ViewModel (if needed), and XIB
3. Inherit from appropriate base classes

**Working with live streaming**:
- Multiple SDK options available in `yxy/Classes/Vendor/`
- Check existing implementations in Live module

## Testing

Tests are in `yxyTests/` and `yxyUITests/` but coverage is minimal. Run tests via Xcode's test navigator (Cmd+6).

## Important Notes

- This is a production app for the Chinese market (uses Baidu Maps, WeChat, etc.)
- Bundle ID: `cn.ulearning.yxy`
- No automated build scripts or CI/CD configuration present
- Mixed architecture requires understanding both native iOS and Cordova patterns
- Answer in Chinese
- 不需要你帮我运行构建命令,我自己手动构建