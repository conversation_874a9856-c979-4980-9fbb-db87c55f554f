PODS:
  - BMKLocationKit (2.0.0)
  - Bugly (2.5.0)
  - DZNEmptyDataSet (1.8.1)
  - EAIntroView (2.13.0):
    - EARestrictedScrollView (~> 1.1.0)
  - EARestrictedScrollView (1.1.0)
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - GDataXML-HTML (1.4.1)
  - HappyDNS (1.0.1)
  - JCore (2.4.0-noidfa)
  - JPush (3.3.6):
    - JCore (< 3.0.0, >= 2.0.0)
  - JSONModel (1.8.0)
  - Masonry (1.1.0)
  - MBProgressHUD (1.1.0)
  - MJExtension (3.0.17)
  - pop (1.0.12)
  - Qiniu (8.4.3):
    - HappyDNS (~> 1.0.0)
  - Reachability (3.2)
  - ReactiveObjC (3.1.1)
  - RegexKitLite (4.0)
  - SakuraKit (1.0.0):
    - SSZipArchive
  - SDWebImage (5.0.2):
    - SDWebImage/Core (= 5.0.2)
  - SDWebImage/Core (5.0.2)
  - SMPageControl (1.2)
  - SSZipArchive (2.1.5)
  - SVProgressHUD (2.2.5)
  - Toast (4.0.0)
  - UMCommon (7.3.6):
    - UMDevice
  - UMDevice (2.2.1)
  - YYCache (1.0.4)
  - YYText (1.0.7)

DEPENDENCIES:
  - BMKLocationKit
  - Bugly
  - DZNEmptyDataSet
  - EAIntroView (~> 2.13.0)
  - FMDB (~> 2.7.5)
  - GDataXML-HTML
  - JCore (= 2.4.0-noidfa)
  - JPush (= 3.3.6)
  - JSONModel
  - Masonry
  - MBProgressHUD (~> 1.1.0)
  - MJExtension
  - pop
  - Qiniu (~> 8.4.3)
  - Reachability (~> 3.2)
  - ReactiveObjC
  - RegexKitLite (~> 4.0)
  - SakuraKit (= 1.0.0)
  - SDWebImage
  - SMPageControl (~> 1.2)
  - SSZipArchive
  - SVProgressHUD
  - Toast (~> 4.0.0)
  - UMCommon
  - UMDevice
  - YYCache
  - YYText (~> 1.0.7)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - BMKLocationKit
    - Bugly
    - DZNEmptyDataSet
    - EAIntroView
    - EARestrictedScrollView
    - FMDB
    - GDataXML-HTML
    - HappyDNS
    - JCore
    - JPush
    - JSONModel
    - Masonry
    - MBProgressHUD
    - MJExtension
    - pop
    - Qiniu
    - Reachability
    - ReactiveObjC
    - RegexKitLite
    - SakuraKit
    - SDWebImage
    - SMPageControl
    - SSZipArchive
    - SVProgressHUD
    - Toast
    - UMCommon
    - UMDevice
    - YYCache
    - YYText

SPEC CHECKSUMS:
  BMKLocationKit: 097814ef672b1e57e86e6c1968d7892fb78002bf
  Bugly: 3ca9f255c01025582df26f9222893b383c7e4b4e
  DZNEmptyDataSet: 9525833b9e68ac21c30253e1d3d7076cc828eaa7
  EAIntroView: 9adedfc6b5c3fd3ebe19766da01dae573e6ce053
  EARestrictedScrollView: b0c2a3f92fb2610bb44d71c5e4893777c89e45ef
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  GDataXML-HTML: a4f11079b48d9fe96ff1fe478943a317185227e2
  HappyDNS: f42060e8712490088020c96d121786478e188fe7
  JCore: 9352ab113845da41bee2af9ad623ea2b4b6d9757
  JPush: a401b946dcc76b37335422487201bc1ecc8dd296
  JSONModel: 02ab723958366a3fd27da57ea2af2113658762e9
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  MBProgressHUD: e7baa36a220447d8aeb12769bf0585582f3866d9
  MJExtension: 74ec83124a68891619fb7ba9c5c811bbf1691076
  pop: d582054913807fd11fd50bfe6a539d91c7e1a55a
  Qiniu: 54d57f6c12c3aed4a2970d0fe8f040856e0751f2
  Reachability: 33e18b67625424e47b6cde6d202dce689ad7af96
  ReactiveObjC: 011caa393aa0383245f2dcf9bf02e86b80b36040
  RegexKitLite: c0e4c12e6e7de0769f3a3ed679f0f61604170661
  SakuraKit: 9ca56ebff8ad0fd8f61af7a340e2c27d691c554a
  SDWebImage: 6764b5fa0f73c203728052955dbefa2bf1f33282
  SMPageControl: 922892813001cfaf059e86e6801f46a967e9ee29
  SSZipArchive: cefe1364104a0231268a5deb8495bdf2861f52f0
  SVProgressHUD: 1428aafac632c1f86f62aa4243ec12008d7a51d6
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  UMCommon: c61df8457c275afd4f8a2fca10c7446d66220ac0
  UMDevice: 053478c4b4d7292f31f0a275c227d3c4007a5571
  YYCache: 8105b6638f5e849296c71f331ff83891a4942952
  YYText: 5c461d709e24d55a182d1441c41dc639a18a4849

PODFILE CHECKSUM: 3eb3e43c51bec56d4516f5b2286f65410b96f64f

COCOAPODS: 1.14.3
