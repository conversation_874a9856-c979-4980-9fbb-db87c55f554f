//
//  SampleHandler.m
//  YxyScreenExtension
//
//  Created by Wen<PERSON> on 2025/4/21.
//  Copyright © 2025 wenhua. All rights reserved.
//


#import "SampleHandler.h"

@interface SampleHandler()

@property (nonatomic, strong) NSUserDefaults *userDefaults;
@property (nonatomic, strong) NSTimer *checkRequestTimer;
@property(nonatomic, strong) CIContext *context;
@property(nonatomic, strong) NSString *requestID;

@end

@implementation SampleHandler

- (void)finishBroadcastWithError:(NSError *)error {
    [self.userDefaults setObject:@"finished_err" forKey:@"recordingStatus"];
    [self.userDefaults synchronize];
}

- (void)broadcastStartedWithSetupInfo:(NSDictionary<NSString *,NSObject *> *)setupInfo {
    [self.userDefaults setObject:@"setup" forKey:@"recordingStatus"];
    [self.userDefaults synchronize];
    
    NSLog(@"broadcastStartedWithSetupInfo");
    [self.userDefaults setObject:@"started" forKey:@"recordingStatus"];
    [self.userDefaults synchronize];
    
    if (self.checkRequestTimer) {
        [self.checkRequestTimer invalidate];
        self.checkRequestTimer = nil;
    }
    
    if ([[NSThread currentThread] isMainThread]) {
        self.checkRequestTimer = [NSTimer scheduledTimerWithTimeInterval:0.1
                                                                      target:self
                                                                    selector:@selector(checkForDataRequest)
                                                                    userInfo:nil
                                                                     repeats:YES];
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            self.checkRequestTimer = [NSTimer scheduledTimerWithTimeInterval:0.1
                                                                          target:self
                                                                        selector:@selector(checkForDataRequest)
                                                                        userInfo:nil
                                                                         repeats:YES];
        });
    }
    
}

- (CIContext *)context {
    if (!_context) {
        _context = [CIContext contextWithOptions:nil];
    }
    return _context;
}

- (NSUserDefaults *)userDefaults {
    if (!_userDefaults) {
        _userDefaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.cn.ulearning.yxy.shared"];
    }
    return _userDefaults;
}

- (void)broadcastPaused {
    // User has requested to pause the broadcast. Samples will stop being delivered.
    NSLog(@"broadcastPaused");
    
    [self.userDefaults setObject:@"paused" forKey:@"recordingStatus"];
    [self.userDefaults synchronize];
}

- (void)broadcastResumed {
    // User has requested to resume the broadcast. Samples delivery will resume.
    NSLog(@"broadcastResumed");
    
    [self.userDefaults setObject:@"resumed" forKey:@"recordingStatus"];
    [self.userDefaults synchronize];
}

- (void)broadcastFinished {
    // User has requested to finish the broadcast.
    NSLog(@"broadcastFinished");
    
    [self.userDefaults setObject:@"finished" forKey:@"recordingStatus"];
    [self.userDefaults synchronize];
}

- (void)processSampleBuffer:(CMSampleBufferRef)sampleBuffer withType:(RPSampleBufferType)sampleBufferType {
    if (sampleBufferType == RPSampleBufferTypeVideo) {
        //如果需要获取图片
        if (self.requestID && self.requestID.length > 0) {
            [self sendFrameToHost:sampleBuffer requestID:self.requestID];
        }
    }
}

- (void)checkForDataRequest {
    NSString *requestID = [self.userDefaults objectForKey:@"requestFrame"];
    NSLog(@"正在检查 requestFrame : %@", requestID);
    if (requestID && requestID.length > 0) {
        NSLog(@"获取一帧图片...");
        [self.userDefaults setObject:@"" forKey:@"requestFrame"];
        [self.userDefaults synchronize];
        
        self.requestID = requestID;
    }
}


- (void)sendFrameToHost:(CMSampleBufferRef)sampleBuffer requestID:(NSString *)requestID {
    CVImageBufferRef imageBuffer = CMSampleBufferGetImageBuffer(sampleBuffer);
    if (imageBuffer) {
        CIImage *ciImage = [CIImage imageWithCVPixelBuffer:imageBuffer];
        CGImageRef cgImage = [self.context createCGImage:ciImage fromRect:[ciImage extent]];
        UIImage *image = [UIImage imageWithCGImage:cgImage];
        CGImageRelease(cgImage);
        cgImage = nil;
        ciImage = nil;
        
        NSData *imageData = UIImageJPEGRepresentation(image, 0.4);
        image = nil;
        NSDictionary *imgInfoDic = @{@"requestID" : requestID, @"imageData" : imageData};
        self.requestID = nil;
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.userDefaults setObject:imgInfoDic forKey:@"imageData"];
            [self.userDefaults synchronize];
        });
    }
}



//- (void)loadBroadcastingApplicationInfoWithCompletion:(void (^)(RPBroadcastApplicationInfo * _Nullable, NSError * _Nullable))completion {
//    NSString *localizedAppName = [[NSBundle mainBundle] localizedStringForKey:@"CFBundleDisplayName" value:nil table:@"InfoPlist"];
//    
//    RPBroadcastApplicationInfo *info = [[RPBroadcastApplicationInfo alloc] init];
//    info.applicationName = localizedAppName;
//    // 设置其他必要的信息
//    // info.applicationURL = ...;
//    // info.applicationBundleIdentifier = ...;
//    
//    completion(info, nil);
//}

@end
