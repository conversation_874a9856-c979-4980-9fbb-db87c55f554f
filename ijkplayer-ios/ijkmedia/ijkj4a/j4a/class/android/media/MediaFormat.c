/*
 * Copyright (C) 2015 <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * https://github.com/Bilibili/jni4android
 * This file is automatically generated by jni4android, do not modify.
 */

#include "MediaFormat.h"

typedef struct J4AC_android_media_MediaFormat {
    jclass id;

    jmethodID constructor_MediaFormat;
    jmethodID method_createVideoFormat;
    jmethodID method_getInteger;
    jmethodID method_setInteger;
    jmethodID method_setByteBuffer;
} J4AC_android_media_MediaFormat;
static J4AC_android_media_MediaFormat class_J4AC_android_media_MediaFormat;

jobject J4AC_android_media_MediaFormat__MediaFormat(JNIEnv *env)
{
    return (*env)->NewObject(env, class_J4AC_android_media_MediaFormat.id, class_J4AC_android_media_MediaFormat.constructor_MediaFormat);
}

jobject J4AC_android_media_MediaFormat__MediaFormat__catchAll(JNIEnv *env)
{
    jobject ret_object = J4AC_android_media_MediaFormat__MediaFormat(env);
    if (J4A_ExceptionCheck__catchAll(env) || !ret_object) {
        return NULL;
    }

    return ret_object;
}

jobject J4AC_android_media_MediaFormat__MediaFormat__asGlobalRef__catchAll(JNIEnv *env)
{
    jobject ret_object   = NULL;
    jobject local_object = J4AC_android_media_MediaFormat__MediaFormat__catchAll(env);
    if (J4A_ExceptionCheck__catchAll(env) || !local_object) {
        ret_object = NULL;
        goto fail;
    }

    ret_object = J4A_NewGlobalRef__catchAll(env, local_object);
    if (!ret_object) {
        ret_object = NULL;
        goto fail;
    }

fail:
    J4A_DeleteLocalRef__p(env, &local_object);
    return ret_object;
}

jobject J4AC_android_media_MediaFormat__createVideoFormat(JNIEnv *env, jstring mime, jint width, jint height)
{
    return (*env)->CallStaticObjectMethod(env, class_J4AC_android_media_MediaFormat.id, class_J4AC_android_media_MediaFormat.method_createVideoFormat, mime, width, height);
}

jobject J4AC_android_media_MediaFormat__createVideoFormat__catchAll(JNIEnv *env, jstring mime, jint width, jint height)
{
    jobject ret_object = J4AC_android_media_MediaFormat__createVideoFormat(env, mime, width, height);
    if (J4A_ExceptionCheck__catchAll(env) || !ret_object) {
        return NULL;
    }

    return ret_object;
}

jobject J4AC_android_media_MediaFormat__createVideoFormat__asGlobalRef__catchAll(JNIEnv *env, jstring mime, jint width, jint height)
{
    jobject ret_object   = NULL;
    jobject local_object = J4AC_android_media_MediaFormat__createVideoFormat__catchAll(env, mime, width, height);
    if (J4A_ExceptionCheck__catchAll(env) || !local_object) {
        ret_object = NULL;
        goto fail;
    }

    ret_object = J4A_NewGlobalRef__catchAll(env, local_object);
    if (!ret_object) {
        ret_object = NULL;
        goto fail;
    }

fail:
    J4A_DeleteLocalRef__p(env, &local_object);
    return ret_object;
}

jobject J4AC_android_media_MediaFormat__createVideoFormat__withCString(JNIEnv *env, const char *mime_cstr__, jint width, jint height)
{
    jobject ret_object = NULL;
    jstring mime = NULL;

    mime = (*env)->NewStringUTF(env, mime_cstr__);
    if (J4A_ExceptionCheck__throwAny(env) || !mime)
        goto fail;

    ret_object = J4AC_android_media_MediaFormat__createVideoFormat(env, mime, width, height);
    if (J4A_ExceptionCheck__throwAny(env) || !ret_object) {
        ret_object = NULL;
        goto fail;
    }

fail:
    J4A_DeleteLocalRef__p(env, &mime);
    return ret_object;
}

jobject J4AC_android_media_MediaFormat__createVideoFormat__withCString__catchAll(JNIEnv *env, const char *mime_cstr__, jint width, jint height)
{
    jobject ret_object = NULL;
    jstring mime = NULL;

    mime = (*env)->NewStringUTF(env, mime_cstr__);
    if (J4A_ExceptionCheck__catchAll(env) || !mime)
        goto fail;

    ret_object = J4AC_android_media_MediaFormat__createVideoFormat__catchAll(env, mime, width, height);
    if (J4A_ExceptionCheck__catchAll(env) || !ret_object) {
        ret_object = NULL;
        goto fail;
    }

fail:
    J4A_DeleteLocalRef__p(env, &mime);
    return ret_object;
}

jobject J4AC_android_media_MediaFormat__createVideoFormat__withCString__asGlobalRef__catchAll(JNIEnv *env, const char *mime_cstr__, jint width, jint height)
{
    jobject ret_object   = NULL;
    jobject local_object = J4AC_android_media_MediaFormat__createVideoFormat__withCString__catchAll(env, mime_cstr__, width, height);
    if (J4A_ExceptionCheck__catchAll(env) || !local_object) {
        ret_object = NULL;
        goto fail;
    }

    ret_object = J4A_NewGlobalRef__catchAll(env, local_object);
    if (!ret_object) {
        ret_object = NULL;
        goto fail;
    }

fail:
    J4A_DeleteLocalRef__p(env, &local_object);
    return ret_object;
}

jint J4AC_android_media_MediaFormat__getInteger(JNIEnv *env, jobject thiz, jstring name)
{
    return (*env)->CallIntMethod(env, thiz, class_J4AC_android_media_MediaFormat.method_getInteger, name);
}

jint J4AC_android_media_MediaFormat__getInteger__catchAll(JNIEnv *env, jobject thiz, jstring name)
{
    jint ret_value = J4AC_android_media_MediaFormat__getInteger(env, thiz, name);
    if (J4A_ExceptionCheck__catchAll(env)) {
        return 0;
    }

    return ret_value;
}

jint J4AC_android_media_MediaFormat__getInteger__withCString(JNIEnv *env, jobject thiz, const char *name_cstr__)
{
    jint ret_value = 0;
    jstring name = NULL;

    name = (*env)->NewStringUTF(env, name_cstr__);
    if (J4A_ExceptionCheck__throwAny(env) || !name)
        goto fail;

    ret_value = J4AC_android_media_MediaFormat__getInteger(env, thiz, name);
    if (J4A_ExceptionCheck__throwAny(env)) {
        ret_value = 0;
        goto fail;
    }

fail:
    J4A_DeleteLocalRef__p(env, &name);
    return ret_value;
}

jint J4AC_android_media_MediaFormat__getInteger__withCString__catchAll(JNIEnv *env, jobject thiz, const char *name_cstr__)
{
    jint ret_value = 0;
    jstring name = NULL;

    name = (*env)->NewStringUTF(env, name_cstr__);
    if (J4A_ExceptionCheck__catchAll(env) || !name)
        goto fail;

    ret_value = J4AC_android_media_MediaFormat__getInteger__catchAll(env, thiz, name);
    if (J4A_ExceptionCheck__catchAll(env)) {
        ret_value = 0;
        goto fail;
    }

fail:
    J4A_DeleteLocalRef__p(env, &name);
    return ret_value;
}

void J4AC_android_media_MediaFormat__setInteger(JNIEnv *env, jobject thiz, jstring name, jint value)
{
    (*env)->CallVoidMethod(env, thiz, class_J4AC_android_media_MediaFormat.method_setInteger, name, value);
}

void J4AC_android_media_MediaFormat__setInteger__catchAll(JNIEnv *env, jobject thiz, jstring name, jint value)
{
    J4AC_android_media_MediaFormat__setInteger(env, thiz, name, value);
    J4A_ExceptionCheck__catchAll(env);
}

void J4AC_android_media_MediaFormat__setInteger__withCString(JNIEnv *env, jobject thiz, const char *name_cstr__, jint value)
{
    jstring name = NULL;

    name = (*env)->NewStringUTF(env, name_cstr__);
    if (J4A_ExceptionCheck__throwAny(env) || !name)
        goto fail;

    J4AC_android_media_MediaFormat__setInteger(env, thiz, name, value);

fail:
    J4A_DeleteLocalRef__p(env, &name);
}

void J4AC_android_media_MediaFormat__setInteger__withCString__catchAll(JNIEnv *env, jobject thiz, const char *name_cstr__, jint value)
{
    jstring name = NULL;

    name = (*env)->NewStringUTF(env, name_cstr__);
    if (J4A_ExceptionCheck__catchAll(env) || !name)
        goto fail;

    J4AC_android_media_MediaFormat__setInteger__catchAll(env, thiz, name, value);

fail:
    J4A_DeleteLocalRef__p(env, &name);
}

void J4AC_android_media_MediaFormat__setByteBuffer(JNIEnv *env, jobject thiz, jstring name, jobject bytes)
{
    (*env)->CallVoidMethod(env, thiz, class_J4AC_android_media_MediaFormat.method_setByteBuffer, name, bytes);
}

void J4AC_android_media_MediaFormat__setByteBuffer__catchAll(JNIEnv *env, jobject thiz, jstring name, jobject bytes)
{
    J4AC_android_media_MediaFormat__setByteBuffer(env, thiz, name, bytes);
    J4A_ExceptionCheck__catchAll(env);
}

void J4AC_android_media_MediaFormat__setByteBuffer__withCString(JNIEnv *env, jobject thiz, const char *name_cstr__, jobject bytes)
{
    jstring name = NULL;

    name = (*env)->NewStringUTF(env, name_cstr__);
    if (J4A_ExceptionCheck__throwAny(env) || !name)
        goto fail;

    J4AC_android_media_MediaFormat__setByteBuffer(env, thiz, name, bytes);

fail:
    J4A_DeleteLocalRef__p(env, &name);
}

void J4AC_android_media_MediaFormat__setByteBuffer__withCString__catchAll(JNIEnv *env, jobject thiz, const char *name_cstr__, jobject bytes)
{
    jstring name = NULL;

    name = (*env)->NewStringUTF(env, name_cstr__);
    if (J4A_ExceptionCheck__catchAll(env) || !name)
        goto fail;

    J4AC_android_media_MediaFormat__setByteBuffer__catchAll(env, thiz, name, bytes);

fail:
    J4A_DeleteLocalRef__p(env, &name);
}

int J4A_loadClass__J4AC_android_media_MediaFormat(JNIEnv *env)
{
    int         ret                   = -1;
    const char *J4A_UNUSED(name)      = NULL;
    const char *J4A_UNUSED(sign)      = NULL;
    jclass      J4A_UNUSED(class_id)  = NULL;
    int         J4A_UNUSED(api_level) = 0;

    if (class_J4AC_android_media_MediaFormat.id != NULL)
        return 0;

    api_level = J4A_GetSystemAndroidApiLevel(env);

    if (api_level < 16) {
        J4A_ALOGW("J4ALoader: Ignore: '%s' need API %d\n", "android.media.MediaFormat", api_level);
        goto ignore;
    }

    sign = "android/media/MediaFormat";
    class_J4AC_android_media_MediaFormat.id = J4A_FindClass__asGlobalRef__catchAll(env, sign);
    if (class_J4AC_android_media_MediaFormat.id == NULL)
        goto fail;

    class_id = class_J4AC_android_media_MediaFormat.id;
    name     = "<init>";
    sign     = "()V";
    class_J4AC_android_media_MediaFormat.constructor_MediaFormat = J4A_GetMethodID__catchAll(env, class_id, name, sign);
    if (class_J4AC_android_media_MediaFormat.constructor_MediaFormat == NULL)
        goto fail;

    class_id = class_J4AC_android_media_MediaFormat.id;
    name     = "createVideoFormat";
    sign     = "(Ljava/lang/String;II)Landroid/media/MediaFormat;";
    class_J4AC_android_media_MediaFormat.method_createVideoFormat = J4A_GetStaticMethodID__catchAll(env, class_id, name, sign);
    if (class_J4AC_android_media_MediaFormat.method_createVideoFormat == NULL)
        goto fail;

    class_id = class_J4AC_android_media_MediaFormat.id;
    name     = "getInteger";
    sign     = "(Ljava/lang/String;)I";
    class_J4AC_android_media_MediaFormat.method_getInteger = J4A_GetMethodID__catchAll(env, class_id, name, sign);
    if (class_J4AC_android_media_MediaFormat.method_getInteger == NULL)
        goto fail;

    class_id = class_J4AC_android_media_MediaFormat.id;
    name     = "setInteger";
    sign     = "(Ljava/lang/String;I)V";
    class_J4AC_android_media_MediaFormat.method_setInteger = J4A_GetMethodID__catchAll(env, class_id, name, sign);
    if (class_J4AC_android_media_MediaFormat.method_setInteger == NULL)
        goto fail;

    class_id = class_J4AC_android_media_MediaFormat.id;
    name     = "setByteBuffer";
    sign     = "(Ljava/lang/String;Ljava/nio/ByteBuffer;)V";
    class_J4AC_android_media_MediaFormat.method_setByteBuffer = J4A_GetMethodID__catchAll(env, class_id, name, sign);
    if (class_J4AC_android_media_MediaFormat.method_setByteBuffer == NULL)
        goto fail;

    J4A_ALOGD("J4ALoader: OK: '%s' loaded\n", "android.media.MediaFormat");
ignore:
    ret = 0;
fail:
    return ret;
}
